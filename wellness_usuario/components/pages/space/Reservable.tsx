"use client";

import { ReservableInt } from "@/styles/ModelTypes";
import { formatearHora } from "@/utils/helpers";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams, useRouter } from "next/navigation";
import React from "react";
import Swal from "sweetalert2";
import { DateTime } from "luxon";

interface Props {
  id: number;
  image: string;
  reservable: ReservableInt;
}

export default function Reservable({ id, image, reservable }: Props) {
  const router = useRouter();
  const search = useSearchParams();

  const spaceName = search.get("name");

  const {} = reservable;
  const hour = formatearHora(new Date(reservable.init_date));
  const endHour = formatearHora(new Date(reservable.end_date));
  const coach = reservable?.admin?.name ?? "";
  const fillQuota = reservable.reservations?.length ?? 0;
  const totalQuota = reservable.quota;

  const reservableDate = DateTime.fromISO(reservable.init_date)
    .plus({ minutes: 5 })
    .toJSDate();
  const nowPlus1 = DateTime.now().plus({ day: 1 }).toJSDate();
  const cannotReserve = nowPlus1 <= reservableDate || fillQuota >= totalQuota;

  function handleReserve() {
    // Verificamos si es posible reservar
    if (fillQuota >= totalQuota) {
      Swal.fire({
        icon: "error",
        title: "Sesión llena",
        text: "Esta sesión ya alcanzó su capacidad máxima",
        confirmButtonColor: "#3B82F6",
      });
      return;
    }

    if (nowPlus1 <= reservableDate) {
      Swal.fire({
        icon: "error",
        title: "No puedes reservar",
        text: "Solo puedes reservar con un día de anticipación",
        confirmButtonColor: "#3B82F6",
      });
      return;
    }

    router.push(`/space/${id}?name=${spaceName}`);
  }

  // Calculate availability percentage for visual indicator
  const availabilityPercentage = ((totalQuota - fillQuota) / totalQuota) * 100;
  const isAlmostFull = availabilityPercentage <= 25 && availabilityPercentage > 0;
  const isFull = fillQuota >= totalQuota;

  return (
    <div className="w-full bg-white rounded-2xl shadow-lg border border-slate-200 p-4 sm:p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      {/* Mobile Layout */}
      <div className="block sm:hidden">
        <div className="flex items-start justify-between mb-4">
          {/* Time and Coach Info */}
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <svg className="w-4 h-4 text-blue-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-slate-900 font-bold text-lg">{hour} - {endHour}</p>
            </div>
            <div className="flex items-center gap-2 mb-3">
              <svg className="w-4 h-4 text-slate-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <p className="text-slate-600 font-medium text-sm">Coach {coach}</p>
            </div>

            {/* Availability Indicator */}
            <div className="flex items-center gap-2">
              <div className="flex-1 bg-slate-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    isFull ? 'bg-red-500' : isAlmostFull ? 'bg-amber-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${Math.max(availabilityPercentage, 5)}%` }}
                ></div>
              </div>
              <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                isFull ? 'bg-red-100 text-red-800' : isAlmostFull ? 'bg-amber-100 text-amber-800' : 'bg-green-100 text-green-800'
              }`}>
                {totalQuota - fillQuota}/{totalQuota}
              </span>
            </div>
          </div>

          {/* Coach Avatar */}
          <div className="relative ml-4 flex-shrink-0">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-0.5">
              <div className="w-full h-full bg-white rounded-xl flex items-center justify-center overflow-hidden">
                <Image
                  src={image}
                  width={40}
                  height={40}
                  className="w-full h-full object-cover rounded-xl"
                  alt="Coach Avatar"
                />
              </div>
            </div>
            {/* Online indicator */}
            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
          </div>
        </div>

        {/* Reserve Button - Mobile */}
        <button
          className={`w-full py-3 px-4 rounded-xl font-semibold text-sm transition-all duration-200 flex items-center justify-center gap-2 ${
            cannotReserve
              ? "bg-slate-100 text-slate-400 cursor-not-allowed"
              : "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl active:scale-95"
          }`}
          type="button"
          onClick={handleReserve}
          disabled={cannotReserve}
        >
          {isFull ? (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
              </svg>
              Sesión Llena
            </>
          ) : nowPlus1 <= reservableDate ? (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              No Disponible
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              Reservar Ahora
            </>
          )}
        </button>
      </div>

      {/* Desktop Layout */}
      <div className="hidden sm:block">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 lg:gap-6">
            {/* Coach Avatar */}
            <div className="relative flex-shrink-0">
              <div className="w-14 h-14 lg:w-16 lg:h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-0.5">
                <div className="w-full h-full bg-white rounded-2xl flex items-center justify-center overflow-hidden">
                  <Image
                    src={image}
                    width={60}
                    height={60}
                    className="w-full h-full object-cover rounded-2xl"
                    alt="Coach Avatar"
                  />
                </div>
              </div>
              {/* Online indicator */}
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
            </div>

            {/* Class Info */}
            <div className="flex flex-col min-w-0 flex-1">
              <div className="flex items-center gap-2 mb-1">
                <svg className="w-5 h-5 text-blue-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-slate-900 font-bold text-lg lg:text-xl">{hour} - {endHour}</p>
              </div>
              <div className="flex items-center gap-2 mb-3">
                <svg className="w-4 h-4 text-slate-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <p className="text-slate-600 font-medium">Coach {coach}</p>
              </div>

              {/* Availability Indicator - Desktop */}
              <div className="flex items-center gap-3">
                <div className="flex-1 max-w-32 bg-slate-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      isFull ? 'bg-red-500' : isAlmostFull ? 'bg-amber-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${Math.max(availabilityPercentage, 5)}%` }}
                  ></div>
                </div>
                <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                  isFull ? 'bg-red-100 text-red-800' : isAlmostFull ? 'bg-amber-100 text-amber-800' : 'bg-green-100 text-green-800'
                }`}>
                  {totalQuota - fillQuota}/{totalQuota} disponibles
                </span>
              </div>
            </div>
          </div>

          {/* Reserve Button - Desktop */}
          <button
            className={`px-6 py-3 lg:px-8 lg:py-4 rounded-xl font-semibold text-sm lg:text-base transition-all duration-200 flex items-center gap-2 flex-shrink-0 ${
              cannotReserve
                ? "bg-slate-100 text-slate-400 cursor-not-allowed"
                : "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transform hover:scale-105"
            }`}
            type="button"
            onClick={handleReserve}
            disabled={cannotReserve}
          >
            {isFull ? (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                </svg>
                Lleno
              </>
            ) : nowPlus1 <= reservableDate ? (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                No disponible
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Reservar
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
