"use client";

import React, { useEffect, useState } from "react";
import Reservable from "@/components/pages/space/Reservable";

import useSWR from "swr";
import { fetcher } from "@/config/fetcher";
import { ReservableInt } from "@/styles/ModelTypes";

import { useParams, useSearchParams, useRouter } from "next/navigation";

import { formatearFecha, generarIdUnico } from "@/utils/helpers";
import Loader from "@/components/shared/Loader";
import { DateTime } from "luxon";

type ReservableDateFormat = [string, ReservableInt[]];

// Enhanced Date Selector Component
function DateSelector({ 
  reservables, 
  selectedDay, 
  onDayChange 
}: { 
  reservables: ReservableDateFormat[], 
  selectedDay: string, 
  onDayChange: (day: string) => void 
}) {
  return (
    <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-4 sm:p-6 mb-6">
      <h3 className="text-lg sm:text-xl font-bold text-slate-800 mb-4 text-center">
        Selecciona una Fecha
      </h3>
      
      {reservables.length > 0 ? (
        <div className="flex items-center gap-3 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100">
          {reservables.map((element, index) => {
            const isSelected = element[0] === selectedDay;
            const date = DateTime.fromISO(element[0]).toJSDate();
            const isToday = DateTime.fromJSDate(date).hasSame(DateTime.now(), 'day');
            const availableSlots = element[1].length;
            
            return (
              <button
                key={generarIdUnico()}
                type="button"
                onClick={() => onDayChange(element[0])}
                className={`flex-shrink-0 min-w-[120px] sm:min-w-[140px] px-4 py-3 rounded-xl text-sm sm:text-base font-medium transition-all duration-200 transform hover:scale-105 ${
                  isSelected
                    ? "bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg"
                    : "bg-slate-50 text-slate-700 hover:bg-blue-50 hover:text-blue-600 border border-slate-200"
                }`}
              >
                <div className="text-center">
                  <div className="font-bold">
                    {formatearFecha(date)}
                  </div>
                  {isToday && (
                    <div className={`text-xs mt-1 ${isSelected ? 'text-blue-100' : 'text-blue-600'}`}>
                      Hoy
                    </div>
                  )}
                  <div className={`text-xs mt-1 ${isSelected ? 'text-blue-100' : 'text-slate-500'}`}>
                    {availableSlots} {availableSlots === 1 ? 'horario' : 'horarios'}
                  </div>
                </div>
              </button>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-slate-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <p className="text-slate-500 font-medium">No hay fechas disponibles</p>
          <p className="text-slate-400 text-sm mt-1">Inténtalo más tarde</p>
        </div>
      )}
    </div>
  );
}

// Enhanced Reservables Grid Component
function ReservablesGrid({ 
  reservables, 
  spaceName 
}: { 
  reservables: ReservableInt[], 
  spaceName: string 
}) {
  if (!reservables || reservables.length === 0) {
    return (
      <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-8 text-center">
        <div className="w-20 h-20 bg-slate-100 rounded-full mx-auto mb-4 flex items-center justify-center">
          <svg className="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-slate-800 mb-2">
          No hay horarios disponibles
        </h3>
        <p className="text-slate-500">
          No se encontraron reservaciones para esta fecha.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-4 sm:p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg sm:text-xl font-bold text-slate-800">
          Horarios Disponibles
        </h3>
        <div className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
          {reservables.length} {reservables.length === 1 ? 'horario' : 'horarios'}
        </div>
      </div>
      
      <div className="space-y-4">
        {reservables.map((reservable) => (
          <Reservable
            key={reservable.id}
            id={reservable.id}
            image="/borregoBlue.png"
            reservable={reservable}
          />
        ))}
      </div>
    </div>
  );
}

// Enhanced Page Header Component
function EnhancedPageHeader({ 
  spaceName, 
  image,
  onBack 
}: { 
  spaceName: string, 
  image: string,
  onBack: () => void 
}) {
  return (
    <div className="relative w-full h-48 sm:h-56 lg:h-64 overflow-hidden bg-gradient-to-br from-blue-600 to-blue-700 mb-6">
      {/* Background Image Overlay */}
      <div 
        className="absolute inset-0 bg-cover bg-center opacity-20"
        style={{ backgroundImage: `url(${image})` }}
      />
      
      {/* Content */}
      <div className="relative z-10 flex items-center h-full px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto w-full">
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="p-2 text-white hover:bg-white/20 rounded-xl transition-all duration-200"
              aria-label="Volver"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div className="text-center flex-1">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2">
                {spaceName}
              </h1>
              <p className="text-blue-100 text-sm sm:text-base">
                Selecciona el horario que mejor se adapte a ti
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Decorative Elements */}
      <div className="absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full backdrop-blur-sm"></div>
      <div className="absolute bottom-4 left-4 w-12 h-12 bg-white/10 rounded-full backdrop-blur-sm"></div>
    </div>
  );
}

export default function Space() {
  const { id: spaceId } = useParams();
  const search = useSearchParams();
  const spaceName = search.get("name") || "Espacio";
  const router = useRouter();

  const { data: reservables } = useSWR<ReservableDateFormat[]>(
    `/reservable/getReservables/${spaceId}`,
    fetcher,
    {
      refreshInterval: 30000, // Refresh every 30 seconds
    }
  );

  const [day, setDay] = useState("");

  // Set the first available day when reservables load
  useEffect(() => {
    if (reservables && reservables.length > 0) {
      setDay(reservables[0][0]);
    }
  }, [reservables]);

  const handleBack = () => {
    router.back();
  };

  // Show loader while data is being fetched
  if (!reservables) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <EnhancedPageHeader spaceName={spaceName} image="/samples/fondo.jpeg" onBack={handleBack} />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center py-20">
            <Loader />
          </div>
        </div>
      </div>
    );
  }

  // Get the selected reservable data
  const selectedReservable = reservables.find((res) => res[0] === day);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Enhanced Page Header */}
      <EnhancedPageHeader spaceName={spaceName} image="/samples/fondo.jpeg" onBack={handleBack} />
      
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        {/* Date Selector */}
        <DateSelector 
          reservables={reservables}
          selectedDay={day}
          onDayChange={setDay}
        />
        
        {/* Reservables Grid */}
        <ReservablesGrid 
          reservables={selectedReservable ? selectedReservable[1] : []}
          spaceName={spaceName}
        />
      </div>
    </div>
  );
}