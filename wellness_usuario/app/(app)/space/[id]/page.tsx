"use client";

import Image from "next/image";
import Link from "next/link";
import React from "react";
import { Fragment, useEffect, useState } from "react";
import { handleError } from "@/utils/errorHandler";
import Swal from "sweetalert2";
import { axiosConfig } from "@/config/axiosConfig";
import clienteAxios from "@/config/clienteAxios";

import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import Loader from "@/components/shared/Loader";
import { formatearHora } from "@/utils/helpers";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import useSWR from "swr";
import { fetcher } from "@/config/fetcher";
import { ReservableInt } from "@/styles/ModelTypes";
import { DateTime } from "luxon";
import ProtectedRoute from "@/components/auth/ProtectedRoute";

// Enhanced Info Card Component
function InfoCard({ 
  title, 
  content, 
  icon, 
  variant = "default" 
}: { 
  title: string, 
  content: string | React.ReactNode, 
  icon: React.ReactNode, 
  variant?: "default" | "primary" | "success" | "warning" 
}) {
  const variantClasses = {
    default: "bg-white border-slate-200",
    primary: "bg-gradient-to-br from-blue-600 to-blue-700 text-white border-blue-700",
    success: "bg-gradient-to-br from-green-500 to-green-600 text-white border-green-600",
    warning: "bg-gradient-to-br from-amber-500 to-amber-600 text-white border-amber-600"
  };

  const textClasses = {
    default: "text-slate-600",
    primary: "text-blue-100",
    success: "text-green-100",
    warning: "text-amber-100"
  };

  return (
    <div className={`rounded-2xl border shadow-lg p-4 sm:p-6 transition-all duration-200 hover:shadow-xl ${variantClasses[variant]}`}>
      <div className="flex items-center gap-3 mb-3">
        <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
          variant === "default" ? "bg-slate-100" : "bg-white/20"
        }`}>
          {icon}
        </div>
        <h3 className={`font-semibold text-sm sm:text-base ${
          variant === "default" ? "text-slate-800" : "text-white"
        }`}>
          {title}
        </h3>
      </div>
      <div className={`text-lg sm:text-xl font-bold ${
        variant === "default" ? "text-slate-900" : "text-white"
      }`}>
        {content}
      </div>
    </div>
  );
}

// Enhanced Header Component
function ReserveHeader({ 
  spaceName, 
  onBack 
}: { 
  spaceName: string, 
  onBack: () => void 
}) {
  return (
    <div className="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-40 backdrop-blur-md bg-white/95">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex items-center gap-4">
          <button
            onClick={onBack}
            className="p-2 text-slate-600 hover:text-blue-600 hover:bg-blue-50 rounded-xl transition-all duration-200"
            aria-label="Volver"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 className="text-xl sm:text-2xl font-bold text-slate-800">
              {spaceName}
            </h1>
            <p className="text-sm text-slate-600">Confirma tu reservación</p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Enhanced Image Gallery Component
function ImageGallery({ 
  image, 
  spaceName 
}: { 
  image: string, 
  spaceName: string 
}) {
  return (
    <div className="relative w-full h-64 sm:h-80 lg:h-96 rounded-2xl overflow-hidden shadow-lg group">
      <Image
        className="object-cover object-center w-full h-full transition-transform duration-300 group-hover:scale-105"
        src={image}
        fill
        alt={`Imagen de ${spaceName}`}
        priority
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
    </div>
  );
}

// Enhanced Reservation Button Component
function ReservationButton({ 
  onReserve, 
  isDisabled, 
  isLoading 
}: { 
  onReserve: () => void, 
  isDisabled: boolean, 
  isLoading: boolean 
}) {
  return (
    <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-6">
      <button
        type="button"
        onClick={onReserve}
        disabled={isDisabled || isLoading}
        className={`w-full py-4 px-6 rounded-xl font-bold text-lg transition-all duration-200 transform ${
          isDisabled || isLoading
            ? "bg-slate-200 text-slate-500 cursor-not-allowed"
            : "bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 hover:scale-105 shadow-lg hover:shadow-xl"
        }`}
      >
        {isLoading ? (
          <div className="flex items-center justify-center gap-2">
            <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
            Procesando...
          </div>
        ) : isDisabled ? (
          "No disponible"
        ) : (
          "Confirmar Reservación"
        )}
      </button>
      
      {isDisabled && (
        <p className="text-sm text-slate-500 text-center mt-3">
          Esta reservación no está disponible en este momento
        </p>
      )}
    </div>
  );
}

export default function Reserve() {
  const { id } = useParams();
  const search = useSearchParams();
  const spaceName = search.get("name") || "Espacio";
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Get reservable data with SWR
  const { data: reservable } = useSWR<ReservableInt>(
    `/reservable/getReservable/${id}`,
    fetcher,
    {
      refreshInterval: 3000,
    }
  );

  const handleBack = () => {
    router.back();
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    
    try {
      const config = axiosConfig();
      if (!config) {
        throw new Error("Sesión vencida, inicia sesión");
      }

      // Create reservation for user
      const { data, status } = await clienteAxios.post(
        `/reservation/reserve/${id}`,
        {},
        config
      );

      if (status !== 200) {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: data.msg,
          confirmButtonColor: "#3B82F6",
        });
        return;
      }

      // Show success message
      await Swal.fire({
        icon: "success",
        title: "¡Reservación exitosa!",
        text: data.msg,
        confirmButtonColor: "#3B82F6",
      });

      // Redirect to reservations page
      router.push("/reservaciones");
    } catch (error: any) {
      handleError(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Show loader while data is being fetched
  if (!reservable) {
    return (
      <ProtectedRoute showLoginPrompt={true}>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
          <Loader />
        </div>
      </ProtectedRoute>
    );
  }

  // Extract variables from reservable
  const { quota, init_date, end_date, admin, reservations } = reservable;
  const fillQuota = reservations?.length ?? 0;

  // Calculate if reservation should be disabled
  const reservableDate = DateTime.fromISO(init_date).plus({ minutes: 5 }).toJSDate();
  const currentDate = DateTime.now().toJSDate();
  const shouldDisable =
    currentDate > reservableDate ||
    DateTime.now().plus({ day: 1 }).toJSDate() <= reservableDate ||
    fillQuota >= quota;

  return (
    <ProtectedRoute showLoginPrompt={true}>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        {/* Enhanced Header */}
        <ReserveHeader spaceName={spaceName} onBack={handleBack} />

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
            {/* Left Column - Image and Details */}
            <div className="space-y-6">
              {/* Image Gallery */}
              <ImageGallery
                image={reservable.space?.image ?? "/samples/fondo.jpeg"}
                spaceName={spaceName}
              />

              {/* Additional Info Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <InfoCard
                  title="Instructor"
                  content={admin?.name || "Por asignar"}
                  variant="default"
                  icon={
                    <svg className="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  }
                />
                <InfoCard
                  title="Ubicación"
                  content={reservable.space?.name || spaceName}
                  variant="default"
                  icon={
                    <svg className="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  }
                />
              </div>
            </div>

            {/* Right Column - Reservation Details */}
            <div className="space-y-6">
              {/* Schedule Card */}
              <InfoCard
                title="Horario de la Sesión"
                content={`${formatearHora(new Date(init_date))} - ${formatearHora(new Date(end_date))}`}
                variant="primary"
                icon={
                  <svg className="w-5 h-5 text-blue-100" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                }
              />

              {/* Occupancy Card */}
              <InfoCard
                title="Disponibilidad"
                content={
                  <div className="flex items-center gap-2">
                    <span>{reservable.onlineQuotaString}</span>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                      fillQuota >= quota ? "bg-red-100 text-red-800" : "bg-green-100 text-green-800"
                    }`}>
                      {fillQuota >= quota ? "Lleno" : "Disponible"}
                    </div>
                  </div>
                }
                variant={fillQuota >= quota ? "warning" : "success"}
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                }
              />

              {/* Reservation Information */}
              <div className="bg-blue-50 rounded-2xl border border-blue-200 p-6">
                <h3 className="font-semibold text-blue-900 mb-4 flex items-center gap-2">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Información Importante
                </h3>
                <ul className="space-y-2 text-sm text-blue-800">
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Llega 5 minutos antes del horario programado</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Trae ropa y calzado deportivo apropiado</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    <span>Puedes cancelar hasta 2 horas antes</span>
                  </li>
                </ul>
              </div>

              {/* Reservation Button */}
              <ReservationButton
                onReserve={handleSubmit}
                isDisabled={shouldDisable}
                isLoading={isLoading}
              />
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}