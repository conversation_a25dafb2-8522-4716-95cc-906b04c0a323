"use client";

import Space from "@/components/pages/index/Space";
import React from "react";

import useSWR from "swr";
import { fetcher } from "@/config/fetcher";
import { SpaceInt } from "@/styles/ModelTypes";

import Spinner from "@/components/shared/Spinner";
import { ProyectionGraphData } from "@/styles/AppTypes";
import ProyectionGraph from "@/components/data/ProyectionGraph";

// Live Status Dashboard Component
function LiveStatusDashboard({ liveStatusData }: { liveStatusData: any }) {
  // Datos mock para cuando no hay datos reales
  const mockLiveStatus = [
    { label: "Ocupado", value: 380, color: "#EF4444" },
    { label: "Disponible", value: 120, color: "#10B981" }
  ];

  // Usar datos reales si están disponibles, sino usar mock
  let statusData = mockLiveStatus;

  if (liveStatusData && Array.isArray(liveStatusData) && liveStatusData.length > 0) {
    statusData = liveStatusData.map((item: any, index: number) => ({
      label: item.label || (index === 0 ? "Ocupado" : "Disponible"),
      value: item.value || 0,
      color: index === 0 ? "#EF4444" : "#10B981"
    }));
  }

  const totalCapacity = statusData.reduce((sum, item) => sum + item.value, 0);
  const occupiedPercentage = totalCapacity > 0
    ? Math.round((statusData[0].value / totalCapacity) * 100)
    : 0;

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-4 sm:p-6">
      <h2 className="text-lg sm:text-xl font-bold text-slate-900 mb-4 text-center">
        Afluencia en Tiempo Real
      </h2>

      <div className="flex flex-col items-center justify-center gap-4">
        {/* Circular Progress */}
        <div className="relative w-32 h-32 sm:w-36 sm:h-36">
          <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="#E2E8F0"
              strokeWidth="8"
              fill="none"
            />
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="#EF4444"
              strokeWidth="8"
              fill="none"
              strokeDasharray={`${occupiedPercentage * 2.83} 283`}
              strokeLinecap="round"
              className="transition-all duration-1000"
            />
          </svg>
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <span className="text-xl sm:text-2xl font-bold text-slate-900">{occupiedPercentage}%</span>
            <span className="text-xs text-slate-500">Ocupado</span>
          </div>
        </div>

        {/* Status Legend */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-6 w-full justify-center">
          {statusData.map((status, index) => (
            <div key={index} className="flex items-center justify-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: status.color }}
              />
              <span className="text-sm font-medium text-slate-700">{status.label}</span>
              <span className="text-lg font-bold text-slate-900">{status.value}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Consolidated Dashboard Component
function ConsolidatedDashboard({ liveStatusData, hourStatus }: { liveStatusData: any, hourStatus: ProyectionGraphData[] | undefined }) {
  return (
    <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-4 sm:p-6 mb-6">
      <h2 className="text-xl sm:text-2xl font-bold text-slate-900 mb-6 text-center">
        Panel de Control
      </h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Live Status */}
        <LiveStatusDashboard liveStatusData={liveStatusData} />
        
        {/* Hourly Chart */}
        <div className="bg-slate-50 rounded-xl p-4 border border-slate-100">
          <h3 className="text-lg font-bold text-slate-800 mb-4 text-center">
            Afluencia por Hora
          </h3>
          <div className="w-full">
            {hourStatus ? (
              <ProyectionGraph data={hourStatus} />
            ) : (
              <div className="flex justify-center py-8">
                <Spinner />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Services Section Component
function ServicesSection({ data }: { data: SpaceInt[] | undefined }) {
  return (
    <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-4 sm:p-6 mb-6">
      <h2 className="text-xl sm:text-2xl font-bold text-slate-800 mb-4 sm:mb-6 text-center">
        Espacios Disponibles
      </h2>
      
      {/* Fixed height container with scroll */}
      <div className="h-64 sm:h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100 hover:scrollbar-thumb-slate-400">
        <div className="space-y-3 pr-2">
          {data ? (
            data.map((space) => (
              <Space
                key={space.id}
                name={space.name}
                id={space.id}
                img={space.image}
              />
            ))
          ) : (
            <div className="flex justify-center py-8">
              <Spinner />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Carousel Placeholder Component
function CarouselSection() {
  return (
    <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl shadow-lg p-6 sm:p-8 mb-6 text-white">
      <div className="text-center">
        <h3 className="text-lg sm:text-xl font-bold mb-2">
          Próximamente: Carousel de Destacados
        </h3>
        <p className="text-blue-100 text-sm sm:text-base">
          Aquí se mostrará contenido destacado y promociones especiales
        </p>
      </div>
      {/* Placeholder for actual carousel component */}
      <div className="mt-4 h-32 bg-white/10 rounded-xl flex items-center justify-center backdrop-blur-sm">
        <div className="text-center">
          <div className="w-12 h-12 bg-white/20 rounded-full mx-auto mb-2 flex items-center justify-center">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <p className="text-sm opacity-80">Contenido del Carousel</p>
        </div>
      </div>
    </div>
  );
}

export default function Index() {
  // Use public endpoints for general data viewing
  const {data:hourStatus} = useSWR<ProyectionGraphData[]>("/space/wellnessAttendances/public",fetcher)
  const { data } = useSWR<SpaceInt[]>(`/space/spaces/public`, fetcher);
  const { data: liveStatusData } = useSWR<any>("/space/liveStatus/public", fetcher);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Welcome Hero Section */}
      <div className="relative w-full h-48 sm:h-56 lg:h-64 overflow-hidden bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center mb-6">
        <div className="text-center text-white px-4">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-2">
            ¡Te damos la bienvenida al Wellness Center!
          </h1>
          <p className="text-blue-100 text-sm sm:text-base">Tu centro de bienestar universitario</p>
        </div>
      </div>

      {/* Main Content Container */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Consolidated Dashboard */}
        <div className="w-full">
          <ConsolidatedDashboard liveStatusData={liveStatusData} hourStatus={hourStatus} />
        </div>

        {/* Carousel Section - Full width on desktop, with margins on mobile */}
        <div className="w-full lg:w-full">
          <CarouselSection />
        </div>

        {/* Services Section with improved layout */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Services take up 2/3 on extra large screens */}
          <div className="xl:col-span-2">
            <ServicesSection data={data} />
          </div>
          
          {/* Additional info section - 1/3 on extra large screens */}
          <div className="xl:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg border border-slate-200 p-4 sm:p-6">
              <h3 className="text-lg font-bold text-slate-800 mb-4 text-center">
                Información Adicional
              </h3>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-xl border border-blue-100">
                  <h4 className="font-semibold text-blue-900 text-sm mb-2">Horarios</h4>
                  <p className="text-blue-700 text-xs">· Lunes a Viernes: 6:00 AM - 10:00 PM</p>
                  <p className="text-blue-700 text-xs">· Cerrado por limpieza: 3:00 PM - 3:30 PM</p>
                  <p className="text-blue-700 text-xs">· Sábados: 10:00 AM - 4:00 PM</p>
                  <p className="text-blue-700 text-xs">· Domingos: Cerrado</p>
                </div>
                <div className="p-4 bg-green-50 rounded-xl border border-green-100">
                  <h4 className="font-semibold text-green-900 text-sm mb-2">Contacto</h4>
                  <p className="text-green-700 text-xs">· <EMAIL></p>
                  <p className="text-green-700 text-xs">· Tel: (81) 8358-2000</p>
                </div>
                <div className="p-4 bg-purple-50 rounded-xl border border-purple-100">
                  <h4 className="font-semibold text-purple-900 text-sm mb-2">Novedades</h4>
                  <p className="text-purple-700 text-xs">· Nuevos materiales disponibles para préstamo</p>
                  <p className="text-purple-700 text-xs">
                    · Ahora puedes colaborar para el desarrollo de la plataforma, {" "}
                    <a
                      href="https://github.com/WellnessCenterTec/wellness_usuario"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-purple-700 underline"
                    >
                      haz click aquí.
                    </a>
                    </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add some bottom spacing */}
      <div className="h-8"></div>
    </div>
  );
}